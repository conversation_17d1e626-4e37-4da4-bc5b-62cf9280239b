$gwx_XC_21=function(_,_v,_n,_p,_s,_wp,_wl,$gwn,$gwl,$gwh,wh,$gstack,$gwrt,gra,grb,TestTest,wfor,_ca,_da,_r,_rz,_o,_oz,_1,_1z,_2,_2z,_m,_mz,nv_getDate,nv_getRegExp,nv_console,nv_parseInt,nv_parseFloat,nv_isNaN,nv_isFinite,nv_decodeURI,nv_decodeURIComponent,nv_encodeURI,nv_encodeURIComponent,$gdc,nv_JSON,_af,_gv,_ai,_grp,_gd,_gapi,$ixc,_ic,_w,_ev,_tsd){return function(path,global){
if(typeof global==='undefined'){if (typeof __GWX_GLOBAL__==='undefined')global={};else global=__GWX_GLOBAL__;}var __WXML_GLOBAL__={};{
}__WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
var e_={}
if(typeof(global.entrys)==='undefined')global.entrys={};e_=global.entrys;
var d_={}
if(typeof(global.defines)==='undefined')global.defines={};d_=global.defines;
var f_={}
if(typeof(global.modules)==='undefined')global.modules={};f_=global.modules || {};
var p_={}
__WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
__WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
__WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
var z=__WXML_GLOBAL__.ops_set.$gwx_XC_21 || [];
function gz$gwx_XC_21_1(){
if( __WXML_GLOBAL__.ops_cached.$gwx_XC_21_1)return __WXML_GLOBAL__.ops_cached.$gwx_XC_21_1
__WXML_GLOBAL__.ops_cached.$gwx_XC_21_1=[];
(function(z){var a=11;function Z(ops){z.push(ops)}
Z([a,[3,'weui-uploader '],[[7],[3,'extClass']]])
Z([3,'weui-uploader__hd'])
Z([[2,'>'],[[7],[3,'maxCount']],[1,1]])
Z([[7],[3,'tips']])
Z([3,'tips'])
Z([3,'weui-uploader__bd'])
Z([[7],[3,'currentFiles']])
Z([3,'*this'])
Z([[6],[[7],[3,'item']],[3,'error']])
Z([3,'previewImage'])
Z([3,'weui-uploader__file weui-uploader__file_status'])
Z([[7],[3,'index']])
Z([3,'#F43530'])
Z([3,'23'])
Z([3,'warn'])
Z([[6],[[7],[3,'item']],[3,'loading']])
Z([[2,'<'],[[6],[[7],[3,'currentFiles']],[3,'length']],[[7],[3,'maxCount']]])
Z([3,'deletePic'])
Z([3,'gallery'])
Z([[7],[3,'previewCurrent']])
Z([1,true])
Z([[7],[3,'previewImageUrls']])
Z([[7],[3,'showPreview']])
Z([[7],[3,'showDelete']])
})(__WXML_GLOBAL__.ops_cached.$gwx_XC_21_1);return __WXML_GLOBAL__.ops_cached.$gwx_XC_21_1
}
__WXML_GLOBAL__.ops_set.$gwx_XC_21=z;
__WXML_GLOBAL__.ops_init.$gwx_XC_21=true;
var x=['./miniprogram_npm/weui-miniprogram/uploader/uploader.wxml'];d_[x[0]]={}
var m0=function(e,s,r,gg){
var z=gz$gwx_XC_21_1()
var tWE=_n('view')
_rz(z,tWE,'class',0,e,s,gg)
var eXE=_n('view')
_rz(z,eXE,'class',1,e,s,gg)
var bYE=_v()
_(eXE,bYE)
if(_oz(z,2,e,s,gg)){bYE.wxVkey=1
}
var oZE=_v()
_(eXE,oZE)
if(_oz(z,3,e,s,gg)){oZE.wxVkey=1
}
else{oZE.wxVkey=2
var x1E=_n('slot')
_rz(z,x1E,'name',4,e,s,gg)
_(oZE,x1E)
}
bYE.wxXCkey=1
oZE.wxXCkey=1
_(tWE,eXE)
var o2E=_n('view')
_rz(z,o2E,'class',5,e,s,gg)
var c4E=_v()
_(o2E,c4E)
var h5E=function(c7E,o6E,o8E,gg){
var a0E=_v()
_(o8E,a0E)
if(_oz(z,8,c7E,o6E,gg)){a0E.wxVkey=1
var tAF=_mz(z,'view',['bindtap',9,'class',1,'data-index',2],[],c7E,o6E,gg)
var eBF=_mz(z,'icon',['color',12,'size',1,'type',2],[],c7E,o6E,gg)
_(tAF,eBF)
_(a0E,tAF)
}
else if(_oz(z,15,c7E,o6E,gg)){a0E.wxVkey=2
}
else{a0E.wxVkey=3
}
a0E.wxXCkey=1
a0E.wxXCkey=3
return o8E
}
c4E.wxXCkey=4
_2z(z,6,h5E,e,s,gg,c4E,'item','index','*this')
var f3E=_v()
_(o2E,f3E)
if(_oz(z,16,e,s,gg)){f3E.wxVkey=1
}
f3E.wxXCkey=1
_(tWE,o2E)
_(r,tWE)
var bCF=_mz(z,'mp-gallery',['binddelete',17,'class',1,'current',2,'hideOnClick',3,'imgUrls',4,'show',5,'showDelete',6],[],e,s,gg)
_(r,bCF)
return r
}
e_[x[0]]=e_[x[0]]||{f:m0,j:[],i:[],ti:[],ic:[]}
if(path&&e_[path]){
return function(env,dd,global){$gwxc=0;var root={"tag":"wx-page"};root.children=[]
;g="$gwx_XC_21";var main=e_[path].f
if (typeof global==="undefined")global={};global.f=$gdc(f_[path],"",1);
try{
main(env,{},root,global);
_tsd(root)
}catch(err){
console.log(err)
}
;g="";
return root;
}
}
}
}(__g.a,__g.b,__g.c,__g.d,__g.e,__g.f,__g.g,__g.h,__g.i,__g.j,__g.k,__g.l,__g.m,__g.n,__g.o,__g.p,__g.q,__g.r,__g.s,__g.t,__g.u,__g.v,__g.w,__g.x,__g.y,__g.z,__g.A,__g.B,__g.C,__g.D,__g.E,__g.F,__g.G,__g.H,__g.I,__g.J,__g.K,__g.L,__g.M,__g.N,__g.O,__g.P,__g.Q,__g.R,__g.S,__g.T,__g.U,__g.V,__g.W,__g.X,__g.Y,__g.Z,__g.aa);if(__vd_version_info__.delayedGwx||false)$gwx_XC_21();	if (__vd_version_info__.delayedGwx) __wxAppCode__['miniprogram_npm/weui-miniprogram/uploader/uploader.wxml'] = [$gwx_XC_21, './miniprogram_npm/weui-miniprogram/uploader/uploader.wxml'];else __wxAppCode__['miniprogram_npm/weui-miniprogram/uploader/uploader.wxml'] = $gwx_XC_21( './miniprogram_npm/weui-miniprogram/uploader/uploader.wxml' );
	;__wxRoute = "miniprogram_npm/weui-miniprogram/uploader/uploader";__wxRouteBegin = true;__wxAppCurrentFile__="miniprogram_npm/weui-miniprogram/uploader/uploader.js";define("miniprogram_npm/weui-miniprogram/uploader/uploader.js",function(require,module,exports,window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,XMLHttpRequest,WebSocket,Reporter,webkit,WeixinJSCore){
"use strict";module.exports=require("../_commons/0.js")([{ids:[22],modules:{158:function(e,t){Component({options:{addGlobalClass:!0},properties:{title:{type:String,value:"图片上传"},sizeType:{type:Array,value:["original","compressed"]},sourceType:{type:Array,value:["album","camera"]},maxSize:{type:Number,value:5242880},maxCount:{type:Number,value:1},files:{type:Array,value:[],observer:function(e){this.setData({currentFiles:e})}},select:{type:null,value:function(){}},upload:{type:null,value:null},tips:{type:String,value:""},extClass:{type:String,value:""},showDelete:{type:Boolean,value:!0}},data:{currentFiles:[],showPreview:!1,previewImageUrls:[]},ready:function(){},methods:{previewImage:function(e){var t=e.currentTarget.dataset.index,a=[];this.data.files.forEach(function(e){a.push(e.url)}),this.setData({previewImageUrls:a,previewCurrent:t,showPreview:!0})},chooseImage:function(){var e=this;this.uploading||wx.chooseImage({count:this.data.maxCount-this.data.files.length,sizeType:this.data.sizeType,sourceType:this.data.sourceType,success:function(t){var a=-1;if(t.tempFiles.forEach(function(t,i){t.size>e.data.maxSize&&(a=i)}),"function"!=typeof e.data.select||!1!==e.data.select(t))if(a>=0)e.triggerEvent("fail",{type:1,errMsg:"chooseImage:fail size exceed ".concat(e.data.maxSize),total:t.tempFilePaths.length,index:a},{});else{var i=wx.getFileSystemManager(),r=t.tempFilePaths.map(function(e){return i.readFileSync(e)}),s={tempFilePaths:t.tempFilePaths,tempFiles:t.tempFiles,contents:r};e.triggerEvent("select",s,{});var l=t.tempFilePaths.map(function(e,a){return{loading:!0,url:t.tempFilePaths[a]||"data:image/jpg;base64,".concat(wx.arrayBufferToBase64(r[a]))}});if(l&&l.length&&"function"==typeof e.data.upload){var n=e.data.files.length,o=e.data.files.concat(l);e.setData({files:o,currentFiles:o}),e.loading=!0,e.data.upload(s).then(function(t){if(e.loading=!1,t.urls){var a=e.data.files;t.urls.forEach(function(e,t){a[n+t].url=e,a[n+t].loading=!1}),e.setData({files:a,currentFiles:o}),e.triggerEvent("success",t,{})}else e.triggerEvent("fail",{type:3,errMsg:"upload file fail, urls not found"},{})}).catch(function(a){e.loading=!1;var i=e.data.files;t.tempFilePaths.forEach(function(e,t){i[n+t].error=!0,i[n+t].loading=!1}),e.setData({files:i,currentFiles:o}),e.triggerEvent("fail",{type:3,errMsg:"upload file fail",error:a},{})})}}},fail:function(t){t.errMsg.indexOf("chooseImage:fail cancel")>=0?e.triggerEvent("cancel",{},{}):(t.type=2,e.triggerEvent("fail",t,{}))}})},deletePic:function(e){var t=e.detail.index,a=this.data.files,i=a.splice(t,1);this.setData({files:a,currentFiles:a}),this.triggerEvent("delete",{index:t,item:i[0]})}}})},16:function(e,t,a){e.exports=a(158)}},entries:[[16,0]]}]);
},{isPage:false,isComponent:true,currentFile:'miniprogram_npm/weui-miniprogram/uploader/uploader.js'});require("miniprogram_npm/weui-miniprogram/uploader/uploader.js");