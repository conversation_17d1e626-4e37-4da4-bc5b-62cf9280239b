$gwx_XC_18=function(_,_v,_n,_p,_s,_wp,_wl,$gwn,$gwl,$gwh,wh,$gstack,$gwrt,gra,grb,TestTest,wfor,_ca,_da,_r,_rz,_o,_oz,_1,_1z,_2,_2z,_m,_mz,nv_getDate,nv_getRegExp,nv_console,nv_parseInt,nv_parseFloat,nv_isNaN,nv_isFinite,nv_decodeURI,nv_decodeURIComponent,nv_encodeURI,nv_encodeURIComponent,$gdc,nv_JSON,_af,_gv,_ai,_grp,_gd,_gapi,$ixc,_ic,_w,_ev,_tsd){return function(path,global){
if(typeof global==='undefined'){if (typeof __GWX_GLOBAL__==='undefined')global={};else global=__GWX_GLOBAL__;}var __WXML_GLOBAL__={};{
}__WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
var e_={}
if(typeof(global.entrys)==='undefined')global.entrys={};e_=global.entrys;
var d_={}
if(typeof(global.defines)==='undefined')global.defines={};d_=global.defines;
var f_={}
if(typeof(global.modules)==='undefined')global.modules={};f_=global.modules || {};
var p_={}
__WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
__WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
__WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
var z=__WXML_GLOBAL__.ops_set.$gwx_XC_18 || [];
function gz$gwx_XC_18_1(){
if( __WXML_GLOBAL__.ops_cached.$gwx_XC_18_1)return __WXML_GLOBAL__.ops_cached.$gwx_XC_18_1
__WXML_GLOBAL__.ops_cached.$gwx_XC_18_1=[];
(function(z){var a=11;function Z(ops){z.push(ops)}
Z([a,[3,'weui-slideview weui-movable-view '],[[2,'?:'],[[7],[3,'icon']],[1,'weui-slideview_icon'],[1,'']],[3,' '],[[7],[3,'extClass']]])
Z([3,'width: 100%;height: 100%;'])
Z([[6],[[7],[3,'handler']],[3,'touchend']])
Z([[6],[[7],[3,'handler']],[3,'touchmove']])
Z([[6],[[7],[3,'handler']],[3,'touchstart']])
Z([[6],[[7],[3,'handler']],[3,'transitionEnd']])
Z([[6],[[7],[3,'handler']],[3,'disableChange']])
Z([[6],[[7],[3,'handler']],[3,'durationChange']])
Z([[6],[[7],[3,'handler']],[3,'sizeReady']])
Z([[6],[[7],[3,'handler']],[3,'rebounceChange']])
Z([[6],[[7],[3,'handler']],[3,'showChange']])
Z([3,'weui-slideview__left left'])
Z([[7],[3,'disable']])
Z([[7],[3,'duration']])
Z([[7],[3,'size']])
Z([[7],[3,'rebounce']])
Z([[7],[3,'show']])
Z([3,'width:100%;'])
Z([[2,'&&'],[[7],[3,'buttons']],[[6],[[7],[3,'buttons']],[3,'length']]])
})(__WXML_GLOBAL__.ops_cached.$gwx_XC_18_1);return __WXML_GLOBAL__.ops_cached.$gwx_XC_18_1
}
__WXML_GLOBAL__.ops_set.$gwx_XC_18=z;
__WXML_GLOBAL__.ops_init.$gwx_XC_18=true;
var x=['./miniprogram_npm/weui-miniprogram/slideview/slideview.wxml'];d_[x[0]]={}
var m0=function(e,s,r,gg){
var z=gz$gwx_XC_18_1()
var hCE=_mz(z,'view',['class',0,'style',1],[],e,s,gg)
var cEE=_mz(z,'view',['bindtouchend',2,'bindtouchmove',1,'bindtouchstart',2,'bindtransitionend',3,'change:disable',4,'change:duration',5,'change:prop',6,'change:rebounce',7,'change:show',8,'class',9,'disable',10,'duration',11,'prop',12,'rebounce',13,'show',14,'style',15],[],e,s,gg)
var oFE=_n('slot')
_(cEE,oFE)
_(hCE,cEE)
var oDE=_v()
_(hCE,oDE)
if(_oz(z,18,e,s,gg)){oDE.wxVkey=1
}
oDE.wxXCkey=1
_(r,hCE)
return r
}
e_[x[0]]=e_[x[0]]||{f:m0,j:[],i:[],ti:[],ic:[]}
if(path&&e_[path]){
return function(env,dd,global){$gwxc=0;var root={"tag":"wx-page"};root.children=[]
;g="$gwx_XC_18";var main=e_[path].f
if (typeof global==="undefined")global={};global.f=$gdc(f_[path],"",1);
try{
main(env,{},root,global);
_tsd(root)
}catch(err){
console.log(err)
}
;g="";
return root;
}
}
}
}(__g.a,__g.b,__g.c,__g.d,__g.e,__g.f,__g.g,__g.h,__g.i,__g.j,__g.k,__g.l,__g.m,__g.n,__g.o,__g.p,__g.q,__g.r,__g.s,__g.t,__g.u,__g.v,__g.w,__g.x,__g.y,__g.z,__g.A,__g.B,__g.C,__g.D,__g.E,__g.F,__g.G,__g.H,__g.I,__g.J,__g.K,__g.L,__g.M,__g.N,__g.O,__g.P,__g.Q,__g.R,__g.S,__g.T,__g.U,__g.V,__g.W,__g.X,__g.Y,__g.Z,__g.aa);if(__vd_version_info__.delayedGwx||false)$gwx_XC_18();	if (__vd_version_info__.delayedGwx) __wxAppCode__['miniprogram_npm/weui-miniprogram/slideview/slideview.wxml'] = [$gwx_XC_18, './miniprogram_npm/weui-miniprogram/slideview/slideview.wxml'];else __wxAppCode__['miniprogram_npm/weui-miniprogram/slideview/slideview.wxml'] = $gwx_XC_18( './miniprogram_npm/weui-miniprogram/slideview/slideview.wxml' );
	;__wxRoute = "miniprogram_npm/weui-miniprogram/slideview/slideview";__wxRouteBegin = true;__wxAppCurrentFile__="miniprogram_npm/weui-miniprogram/slideview/slideview.js";define("miniprogram_npm/weui-miniprogram/slideview/slideview.js",function(require,module,exports,window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,XMLHttpRequest,WebSocket,Reporter,webkit,WeixinJSCore){
"use strict";module.exports=require("../_commons/0.js")([{ids:[19],modules:{13:function(t,e,n){t.exports=n(134)},134:function(t,e){Component({options:{addGlobalClass:!0,multipleSlots:!0},properties:{extClass:{type:String,value:""},buttons:{type:Array,value:[],observer:function(){this.addClassNameForButton()}},disable:{type:Boolean,value:!1},icon:{type:Boolean,value:!1},show:{type:Boolean,value:!1},duration:{type:Number,value:350},throttle:{type:Number,value:40},rebounce:{type:Number,value:0}},data:{size:null},ready:function(){this.updateRight(),this.addClassNameForButton()},methods:{updateRight:function(){var t=this,e=this.data;wx.createSelectorQuery().in(this).select(".left").boundingClientRect(function(n){wx.createSelectorQuery().in(t).selectAll(".btn").boundingClientRect(function(o){t.setData({size:{buttons:o,button:n,show:e.show,disable:e.disable,throttle:e.throttle,rebounce:e.rebounce}})}).exec()}).exec()},addClassNameForButton:function(){var t=this.data,e=t.buttons,n=t.icon;e.forEach(function(t){n?t.className="":"warn"===t.type?t.className="weui-slideview__btn-group_warn":t.className="weui-slideview__btn-group_default"}),this.setData({buttons:e})},buttonTapByWxs:function(t){this.triggerEvent("buttontap",t,{})},hide:function(){this.triggerEvent("hide",{},{})},show:function(){this.triggerEvent("show",{},{})},transitionEnd:function(){}}})}},entries:[[13,0]]}]);
},{isPage:false,isComponent:true,currentFile:'miniprogram_npm/weui-miniprogram/slideview/slideview.js'});require("miniprogram_npm/weui-miniprogram/slideview/slideview.js");