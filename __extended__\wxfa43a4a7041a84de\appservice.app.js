var __wxAppData=__wxAppData||{};var __wxRoute=__wxRoute||"";var __wxRouteBegin=__wxRouteBegin||"";var __wxAppCode__=__wxAppCode__||{};var global=global||{};var __WXML_GLOBAL__=__WXML_GLOBAL__||{entrys:{},defines:{},modules:{},ops:[],wxs_nf_init:undefined,total_ops:0};var __wxAppCurrentFile__=__wxAppCurrentFile__||"";var Component=Component||function(){};var definePlugin=definePlugin||function(){};var requirePlugin=requirePlugin||function(){};var Behavior=Behavior||function(){};var __vd_version_info__=__vd_version_info__||{};var __GWX_GLOBAL__=__GWX_GLOBAL__||{};
/*v0.5vv_20211229_syb_scopedata*/global.__wcc_version__='v0.5vv_20211229_syb_scopedata';global.__wcc_version_info__={"customComponents":true,"fixZeroRpx":true,"propValueDeepCopy":false};
var $gwxc
var $gaic={}
$gwx=function(_,_v,_n,_p,_s,_wp,_wl,$gwn,$gwl,$gwh,wh,$gstack,$gwrt,gra,grb,TestTest,wfor,_ca,_da,_r,_rz,_o,_oz,_1,_1z,_2,_2z,_m,_mz,nv_getDate,nv_getRegExp,nv_console,nv_parseInt,nv_parseFloat,nv_isNaN,nv_isFinite,nv_decodeURI,nv_decodeURIComponent,nv_encodeURI,nv_encodeURIComponent,$gdc,nv_JSON,_af,_gv,_ai,_grp,_gd,_gapi,$ixc,_ic,_w,_ev,_tsd){return function(path,global){
if(typeof global==='undefined'){if (typeof __GWX_GLOBAL__==='undefined')global={};else global=__GWX_GLOBAL__;}if(typeof __WXML_GLOBAL__ === 'undefined') {__WXML_GLOBAL__={};
}__WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
var e_={}
if(typeof(global.entrys)==='undefined')global.entrys={};e_=global.entrys;
var d_={}
if(typeof(global.defines)==='undefined')global.defines={};d_=global.defines;
var f_={}
if(typeof(global.modules)==='undefined')global.modules={};f_=global.modules || {};
var p_={}
__WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
__WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
__WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
var z=__WXML_GLOBAL__.ops_set.$gwx || [];
__WXML_GLOBAL__.ops_set.$gwx=z;
__WXML_GLOBAL__.ops_init.$gwx=true;
var nv_require=function(){var nnm={"m_./miniprogram_npm/weui-miniprogram/actionsheet/actionsheet.wxml:utils":np_1,"m_./miniprogram_npm/weui-miniprogram/icon/icon.wxml:utils":np_2,"p_./miniprogram_npm/weui-miniprogram/_commons/slideview.5abc910a.wxs":np_0,};var nom={};return function(n){if(n[0]==='p'&&n[1]==='_'&&f_[n.slice(2)])return f_[n.slice(2)];return function(){if(!nnm[n]) return undefined;try{if(!nom[n])nom[n]=nnm[n]();return nom[n];}catch(e){e.message=e.message.replace(/nv_/g,'');var tmp = e.stack.substring(0,e.stack.lastIndexOf(n));e.stack = tmp.substring(0,tmp.lastIndexOf('\n'));e.stack = e.stack.replace(/\snv_/g,' ');e.stack = $gstack(e.stack);e.stack += '\n    at ' + n.substring(2);console.error(e);}
}}}()
f_['./miniprogram_npm/weui-miniprogram/_commons/slideview.5abc910a.wxs'] = nv_require("p_./miniprogram_npm/weui-miniprogram/_commons/slideview.5abc910a.wxs");
function np_0(){var nv_module={nv_exports:{}};var nv_touchstart = (function (nv_event,nv_ownerInstance){var nv_ins = nv_event.nv_instance;var nv_st = nv_ins.nv_getState();if (nv_st.nv_disable)return;;if (!nv_st.nv_size)return;;nv_st.nv_isMoving = true;nv_st.nv_startX = nv_event.nv_touches[(0)].nv_pageX;nv_st.nv_startY = nv_event.nv_touches[(0)].nv_pageY;nv_st.nv_firstAngle = 0});var nv_touchmove = (function (nv_event,nv_ownerInstance){var nv_ins = nv_event.nv_instance;var nv_st = nv_ins.nv_getState();if (!nv_st.nv_size || !nv_st.nv_isMoving || nv_st.nv_disable)return;;var nv_pagex = nv_event.nv_touches[(0)].nv_pageX - nv_st.nv_startX;var nv_pagey = nv_event.nv_touches[(0)].nv_pageY - nv_st.nv_startY;if (nv_st.nv_firstAngle === 0){nv_st.nv_firstAngle = Math.nv_abs(nv_pagex) - Math.nv_abs(nv_pagey)};if (nv_st.nv_firstAngle < 0){return};var nv_movex = nv_pagex > 0 ? Math.nv_min(nv_st.nv_max,nv_pagex):Math.nv_max(-nv_st.nv_max,nv_pagex);if (nv_st.nv_out){if (nv_movex < 0)return;;nv_ins.nv_setStyle(({'nv_transform':'translateX(' + (nv_st.nv_transformx + nv_movex) + 'px)','nv_transition':'',}));var nv_btns = nv_ownerInstance.nv_selectAllComponents('.btn');var nv_transformTotal = 0;var nv_len = nv_btns.nv_length;var nv_i = nv_len - 1;for(;nv_i >= 0;nv_i--){var nv_transform = nv_st.nv_size.nv_buttons[((nt_4=(nv_i),null==nt_4?undefined:'number'=== typeof nt_4?nt_4:"nv_"+nt_4))].nv_width / nv_st.nv_max * nv_movex;var nv_transformx = nv_st.nv_size.nv_buttons[((nt_5=(nv_i),null==nt_5?undefined:'number'=== typeof nt_5?nt_5:"nv_"+nt_5))].nv_max - Math.nv_min(nv_st.nv_size.nv_buttons[((nt_6=(nv_i),null==nt_6?undefined:'number'=== typeof nt_6?nt_6:"nv_"+nt_6))].nv_max,nv_transform + nv_transformTotal);nv_btns[((nt_7=(nv_i),null==nt_7?undefined:'number'=== typeof nt_7?nt_7:"nv_"+nt_7))].nv_setStyle(({'nv_transform':'translateX(' + (-nv_transformx) + 'px)','nv_transition':'',}));nv_transformTotal += nv_transform};return(false)};if (nv_movex > 0)nv_movex = 0;;nv_ins.nv_setStyle(({'nv_transform':'translateX(' + nv_movex + 'px)','nv_transition':'',}));nv_st.nv_transformx = nv_movex;var nv_btns = nv_ownerInstance.nv_selectAllComponents('.btn');var nv_transformTotal = 0;var nv_len = nv_btns.nv_length;var nv_i = nv_len - 1;for(;nv_i >= 0;nv_i--){var nv_transform = nv_st.nv_size.nv_buttons[((nt_8=(nv_i),null==nt_8?undefined:'number'=== typeof nt_8?nt_8:"nv_"+nt_8))].nv_width / nv_st.nv_max * nv_movex;var nv_transformx = Math.nv_max(-nv_st.nv_size.nv_buttons[((nt_9=(nv_i),null==nt_9?undefined:'number'=== typeof nt_9?nt_9:"nv_"+nt_9))].nv_max,nv_transform + nv_transformTotal);nv_btns[((nt_10=(nv_i),null==nt_10?undefined:'number'=== typeof nt_10?nt_10:"nv_"+nt_10))].nv_setStyle(({'nv_transform':'translateX(' + nv_transformx + 'px)','nv_transition':'',}));nv_st.nv_size.nv_buttons[((nt_11=(nv_i),null==nt_11?undefined:'number'=== typeof nt_11?nt_11:"nv_"+nt_11))].nv_transformx = nv_transformx;nv_transformTotal += nv_transform};return(false)});var nv_touchend = (function (nv_event,nv_ownerInstance){var nv_ins = nv_event.nv_instance;var nv_st = nv_ins.nv_getState();if (!nv_st.nv_size || !nv_st.nv_isMoving || nv_st.nv_disable)return;;if (nv_st.nv_firstAngle < 0){return};var nv_duration = nv_st.nv_duration / 1000;nv_st.nv_isMoving = false;var nv_btns = nv_ownerInstance.nv_selectAllComponents('.btn');var nv_len = nv_btns.nv_length;var nv_i = nv_len - 1;if (Math.nv_abs(nv_event.nv_changedTouches[(0)].nv_pageX - nv_st.nv_startX) < nv_st.nv_throttle || nv_event.nv_changedTouches[(0)].nv_pageX - nv_st.nv_startX > 0){nv_st.nv_out = false;nv_ins.nv_setStyle(({'nv_transform':'translate3d(0px, 0, 0)','nv_transition':'transform ' + (nv_duration) + 's',}));for(;nv_i >= 0;nv_i--){nv_btns[((nt_14=(nv_i),null==nt_14?undefined:'number'=== typeof nt_14?nt_14:"nv_"+nt_14))].nv_setStyle(({'nv_transform':'translate3d(0px, 0, 0)','nv_transition':'transform ' + (nv_duration) + 's',}))};nv_ownerInstance.nv_callMethod('hide');return};nv_showButtons(nv_ins,nv_ownerInstance,nv_duration);nv_ownerInstance.nv_callMethod('show')});var nv_REBOUNCE_TIME = 0.2;var nv_showButtons = (function (nv_ins,nv_ownerInstance,nv_withDuration){var nv_st = nv_ins.nv_getState();if (!nv_st.nv_size)return;;var nv_rebounceTime = nv_st.nv_rebounce ? nv_REBOUNCE_TIME:0;var nv_movex = nv_st.nv_max;nv_st.nv_out = true;var nv_btns = nv_ownerInstance.nv_selectAllComponents('.btn');var nv_rebounce = nv_st.nv_rebounce || 0;var nv_len = nv_btns.nv_length;var nv_i = nv_len - 1;nv_ins.nv_setStyle(({'nv_transform':'translate3d(' + (-nv_movex - nv_rebounce) + 'px, 0, 0)','nv_transition':'transform ' + (nv_withDuration) + 's',}));nv_st.nv_transformx = -nv_movex;var nv_transformTotal = 0;for(;nv_i >= 0;nv_i--){var nv_transform = nv_st.nv_size.nv_buttons[((nt_15=(nv_i),null==nt_15?undefined:'number'=== typeof nt_15?nt_15:"nv_"+nt_15))].nv_width / nv_st.nv_max * nv_movex;var nv_transformx = (-(nv_transform + nv_transformTotal));nv_btns[((nt_16=(nv_i),null==nt_16?undefined:'number'=== typeof nt_16?nt_16:"nv_"+nt_16))].nv_setStyle(({'nv_transform':'translate3d(' + nv_transformx + 'px, 0, 0)','nv_transition':'transform ' + (nv_withDuration ? nv_withDuration + nv_rebounceTime:nv_withDuration) + 's',}));nv_st.nv_size.nv_buttons[((nt_17=(nv_i),null==nt_17?undefined:'number'=== typeof nt_17?nt_17:"nv_"+nt_17))].nv_transformx = nv_transformx;nv_transformTotal += nv_transform}});var nv_innerHideButton = (function (nv_ownerInstance){var nv_ins = nv_ownerInstance.nv_selectComponent('.left');var nv_st = nv_ins.nv_getState();if (!nv_st.nv_size)return;;var nv_duration = nv_st.nv_duration ? nv_st.nv_duration / 1000:0;var nv_btns = nv_ownerInstance.nv_selectAllComponents('.btn');var nv_len = nv_btns.nv_length;var nv_i = nv_len - 1;nv_ins.nv_setStyle(({'nv_transform':'translate3d(0px, 0, 0)','nv_transition':'transform ' + (nv_duration) + 's',}));nv_st.nv_transformx = 0;nv_st.nv_out = false;for(;nv_i >= 0;nv_i--){nv_btns[((nt_18=(nv_i),null==nt_18?undefined:'number'=== typeof nt_18?nt_18:"nv_"+nt_18))].nv_setStyle(({'nv_transform':'translate3d(0px, 0, 0)','nv_transition':'transform ' + (nv_duration) + 's',}));nv_st.nv_size.nv_buttons[((nt_19=(nv_i),null==nt_19?undefined:'number'=== typeof nt_19?nt_19:"nv_"+nt_19))].nv_transformx = 0}});var nv_hideButton = (function (nv_event,nv_ownerInstance){nv_innerHideButton(nv_ownerInstance);nv_ownerInstance.nv_callMethod('buttonTapByWxs',({nv_index:nv_event.nv_currentTarget.nv_dataset.nv_index,nv_data:nv_event.nv_currentTarget.nv_dataset.nv_data,}));return(false)});var nv_sizeReady = (function (nv_newVal,nv_oldVal,nv_ownerInstance,nv_ins){var nv_st = nv_ins.nv_getState();if (nv_newVal && nv_newVal.nv_button && nv_newVal.nv_buttons){nv_st.nv_size = nv_newVal;nv_st.nv_transformx = 0;var nv_max = 0;var nv_len = nv_newVal.nv_buttons.nv_length;var nv_i = nv_newVal.nv_buttons.nv_length - 1;var nv_total = 0;for(;nv_i >= 0;nv_i--){nv_max += nv_newVal.nv_buttons[((nt_20=(nv_i),null==nt_20?undefined:'number'=== typeof nt_20?nt_20:"nv_"+nt_20))].nv_width;nv_total += nv_newVal.nv_buttons[((nt_21=(nv_i),null==nt_21?undefined:'number'=== typeof nt_21?nt_21:"nv_"+nt_21))].nv_width;nv_newVal.nv_buttons[((nt_22=(nv_i),null==nt_22?undefined:'number'=== typeof nt_22?nt_22:"nv_"+nt_22))].nv_max = nv_total;nv_newVal.nv_buttons[((nt_23=(nv_i),null==nt_23?undefined:'number'=== typeof nt_23?nt_23:"nv_"+nt_23))].nv_transformx = 0};nv_st.nv_throttle = nv_st.nv_size.nv_throttle || 40;nv_st.nv_rebounce = nv_st.nv_size.nv_rebounce;nv_st.nv_max = nv_max;nv_ownerInstance.nv_selectComponent('.right').nv_setStyle(({'nv_line-height':nv_newVal.nv_button.nv_height + 'px',nv_left:(nv_newVal.nv_button.nv_width) + 'px',nv_width:nv_max + 'px',}));if (!nv_st.nv_size.nv_disable && nv_st.nv_size.nv_show){nv_showButtons(nv_ins,nv_ownerInstance)}}});var nv_disableChange = (function (nv_newVal,nv_oldVal,nv_ownerInstance,nv_ins){var nv_st = nv_ins.nv_getState();nv_st.nv_disable = nv_newVal});var nv_durationChange = (function (nv_newVal,nv_oldVal,nv_ownerInstance,nv_ins){var nv_st = nv_ins.nv_getState();nv_st.nv_duration = nv_newVal || 400});var nv_showChange = (function (nv_newVal,nv_oldVal,nv_ownerInstance,nv_ins){var nv_st = nv_ins.nv_getState();nv_st.nv_show = nv_newVal;if (nv_st.nv_disable)return;;if (nv_st.nv_show){nv_showButtons(nv_ins,nv_ownerInstance,nv_st.nv_duration)} else {nv_innerHideButton(nv_ownerInstance)}});var nv_rebounceChange = (function (nv_newVal,nv_oldVal,nv_ownerInstance,nv_ins){var nv_st = nv_ins.nv_getState();nv_st.nv_rebounce = nv_newVal});var nv_transitionEnd = (function (nv_event,nv_ownerInstance){var nv_ins = nv_event.nv_instance;var nv_st = nv_ins.nv_getState();if (nv_st.nv_out && nv_st.nv_rebounce){nv_ins.nv_setStyle(({'nv_transform':'translate3d(' + (-nv_st.nv_max) + 'px, 0, 0)','nv_transition':'transform ' + nv_REBOUNCE_TIME + 's',}))}});nv_module.nv_exports = ({nv_touchstart:nv_touchstart,nv_touchmove:nv_touchmove,nv_touchend:nv_touchend,nv_hideButton:nv_hideButton,nv_sizeReady:nv_sizeReady,nv_disableChange:nv_disableChange,nv_durationChange:nv_durationChange,nv_showChange:nv_showChange,nv_rebounceChange:nv_rebounceChange,nv_transitionEnd:nv_transitionEnd,});return nv_module.nv_exports;}

f_['./miniprogram_npm/weui-miniprogram/actionsheet/actionsheet.wxml']={};
f_['./miniprogram_npm/weui-miniprogram/actionsheet/actionsheet.wxml']['utils'] =nv_require("m_./miniprogram_npm/weui-miniprogram/actionsheet/actionsheet.wxml:utils");
function np_1(){var nv_module={nv_exports:{}};var nv_join = (function (nv_a,nv_b){return(nv_a + nv_b)});var nv_isNotSlot = (function (nv_v){return(typeof nv_v !== 'string')});nv_module.nv_exports = ({nv_join:nv_join,nv_isNotSlot:nv_isNotSlot,});return nv_module.nv_exports;}

f_['./miniprogram_npm/weui-miniprogram/icon/icon.wxml']={};
f_['./miniprogram_npm/weui-miniprogram/icon/icon.wxml']['utils'] =nv_require("m_./miniprogram_npm/weui-miniprogram/icon/icon.wxml:utils");
function np_2(){var nv_module={nv_exports:{}};var nv_double = (function (nv_a){return(2 * nv_a)});var nv_ifSpecialIcon = (function (nv_v){return(nv_v === 'arrow' || nv_v === 'back')});nv_module.nv_exports = ({nv_double:nv_double,nv_ifSpecialIcon:nv_ifSpecialIcon,});return nv_module.nv_exports;}

f_['./miniprogram_npm/weui-miniprogram/slideview/slideview.wxml']={};
f_['./miniprogram_npm/weui-miniprogram/slideview/slideview.wxml']['handler'] =f_['./miniprogram_npm/weui-miniprogram/_commons/slideview.5abc910a.wxs'] || nv_require("p_./miniprogram_npm/weui-miniprogram/_commons/slideview.5abc910a.wxs");
f_['./miniprogram_npm/weui-miniprogram/slideview/slideview.wxml']['handler']();

var x=[];if(path&&e_[path]){
return function(env,dd,global){$gwxc=0;var root={"tag":"wx-page"};root.children=[]
;g="$gwx";var main=e_[path].f
if (typeof global==="undefined")global={};global.f=$gdc(f_[path],"",1);
try{
main(env,{},root,global);
_tsd(root)
}catch(err){
console.log(err)
}
;g="";
return root;
}
}
}
}(__g.a,__g.b,__g.c,__g.d,__g.e,__g.f,__g.g,__g.h,__g.i,__g.j,__g.k,__g.l,__g.m,__g.n,__g.o,__g.p,__g.q,__g.r,__g.s,__g.t,__g.u,__g.v,__g.w,__g.x,__g.y,__g.z,__g.A,__g.B,__g.C,__g.D,__g.E,__g.F,__g.G,__g.H,__g.I,__g.J,__g.K,__g.L,__g.M,__g.N,__g.O,__g.P,__g.Q,__g.R,__g.S,__g.T,__g.U,__g.V,__g.W,__g.X,__g.Y,__g.Z,__g.aa);if(__vd_version_info__.delayedGwx||true)$gwx();;__wxRoute = undefined;__wxRouteBegin = undefined;__wxAppCurrentFile__=undefined;define("miniprogram_npm/weui-miniprogram/_commons/0.js",function(require,module,exports,window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,XMLHttpRequest,WebSocket,Reporter,webkit,WeixinJSCore){
"use strict";var e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};module.exports=function(t){function r(e){if(n[e])return n[e].exports;var o=n[e]={i:e,l:!1,exports:{}};return t[e].call(o.exports,o,o.exports,r),o.l=!0,o.exports}var n={},o={0:0};return r.m=t,r.c=n,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(t,n){if(1&n&&(t=r(t)),8&n)return t;if(4&n&&"object"==(void 0===t?"undefined":e(t))&&t&&t.__esModule)return t;var o=Object.create(null);if(r.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),2&n&&"string"!=typeof t)for(var u in t)r.d(o,u,function(e){return t[e]}.bind(null,u));return o},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r.oe=function(e){process.nextTick(function(){throw e})},function(e){for(var n,u,f,i,l,c,p,y,a,s,d,b=0,m=0,v=[];b<e.length;++b){for(i=(f=e[b]).ids,c=f.modules,p=f.entries||[],n=0;n<i.length;n++)l=i[n],o[l]=0;for(y in c)Object.prototype.hasOwnProperty.call(c,y)&&(t[y]=c[y]);v.push.apply(v,p||[])}for(;m<v.length;m++){for(a=v[m],s=!0,n=1;n<a.length;n++)d=a[n],0!==o[d]&&(s=!1);s&&(v.splice(m--,1),u=r(r.s=a[0]))}return u}}([]);
},{isPage:false,isComponent:false,currentFile:'miniprogram_npm/weui-miniprogram/_commons/0.js'});;__wxRoute = undefined;__wxRouteBegin = undefined;__wxAppCurrentFile__=undefined;