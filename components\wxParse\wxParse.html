	<style> </style> 	<page></page> 	<script> 	var __setCssStartTime__ = Date.now();	setCssToHead([[2,"./components/wxParse/wxParse.wxss"]],undefined,{path:"./components/wxParse/wxParse.wxss"})()
	var __setCssEndTime__ = Date.now();	(function() { 		var gf = $gwx( './components/wxParse/wxParse.wxml' ); 		if (window.__wxAppCodeReadyCallback__) { 			window.__wxAppCodeReadyCallback__(gf); 		} else { 			document.dispatchEvent(new CustomEvent( "generateFuncReady", { 				detail: { 					generateFunc: gf 			}})); 		} 	})(); 	</script> 	