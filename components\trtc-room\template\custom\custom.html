	<style> </style> 	<page></page> 	<script> 	var __setCssStartTime__ = Date.now();	setCssToHead([[2,"./components/trtc-room/template/custom/custom.wxss"]],undefined,{path:"./components/trtc-room/template/custom/custom.wxss"})()
	var __setCssEndTime__ = Date.now();	(function() { 		var gf = $gwx( './components/trtc-room/template/custom/custom.wxml' ); 		if (window.__wxAppCodeReadyCallback__) { 			window.__wxAppCodeReadyCallback__(gf); 		} else { 			document.dispatchEvent(new CustomEvent( "generateFuncReady", { 				detail: { 					generateFunc: gf 			}})); 		} 	})(); 	</script> 	