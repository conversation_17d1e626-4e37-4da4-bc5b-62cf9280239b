	<style> </style> 	<page></page> 	<script> 	var __setCssStartTime__ = Date.now();	setCssToHead([[2,"./components/trtc-room/template/1v1/1v1.wxss"]],undefined,{path:"./components/trtc-room/template/1v1/1v1.wxss"})()
	var __setCssEndTime__ = Date.now();	(function() { 		var gf = $gwx( './components/trtc-room/template/1v1/1v1.wxml' ); 		if (window.__wxAppCodeReadyCallback__) { 			window.__wxAppCodeReadyCallback__(gf); 		} else { 			document.dispatchEvent(new CustomEvent( "generateFuncReady", { 				detail: { 					generateFunc: gf 			}})); 		} 	})(); 	</script> 	