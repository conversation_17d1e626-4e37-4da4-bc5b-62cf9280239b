	<style> </style> 	<page></page> 	<script> 	var __setCssStartTime__ = Date.now();	setCssToHead([[2,"./components/trtc-room/template/grid/grid.wxss"]],"Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./components/trtc-room/template/grid/grid.wxss:1:8163)",{path:"./components/trtc-room/template/grid/grid.wxss"})()
	var __setCssEndTime__ = Date.now();	(function() { 		var gf = $gwx( './components/trtc-room/template/grid/grid.wxml' ); 		if (window.__wxAppCodeReadyCallback__) { 			window.__wxAppCodeReadyCallback__(gf); 		} else { 			document.dispatchEvent(new CustomEvent( "generateFuncReady", { 				detail: { 					generateFunc: gf 			}})); 		} 	})(); 	</script> 	