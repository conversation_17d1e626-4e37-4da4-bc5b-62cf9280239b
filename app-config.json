{"subPackages": [{"root": "packageA/", "plugins2": {"healthCardPlugins": {"version": "3.54.1", "provider": "wxee969de81bba9a45"}}}, {"root": "packageB/"}], "entryPagePath": "pages/customindex/customindex.html", "pages": ["pages/customindex/customindex", "pages/index/index", "pages/mine/mine", "pages/registrationNotice1/registrationNotice1", "pages/register/register", "pages/login/login", "pages/doctorInfo/doctorInfo", "pages/diagnosisroom/diagnosisroom", "pages/diagnosisInfo/diagnosisInfo", "pages/wzrecords/wzrecords", "pages/drugsDetail/drugsDetail", "pages/perinfojfjl/perinfojfjl", "pages/prescription/prescription", "pages/prescriptioninfo/prescriptioninfo", "pages/room/room", "pages/myPrescription/myPrescription", "pages/registration/registration", "pages/registrationDoc/registrationDoc", "pages/registeredinfo/registeredinfo", "pages/reservationrecord/reservationrecord", "pages/reservationcancel/reservationcancel", "pages/reservationinfo/reservationinfo", "pages/reservationpay/reservationpay", "pages/onlinMedicine/onlinMedicine", "pages/custommine/custommine", "pages/notice/notice", "pages/messageDetail/messageDetail", "packageA/pages/address/address", "packageA/pages/addressmanage/addressmanage", "packageA/pages/articlelist/articlelist", "packageA/pages/article/article", "packageA/pages/articalinfo/articalinfo", "packageA/pages/addMedicineRemind/addMedicineRemind", "packageA/pages/allarticle/allarticle", "packageA/pages/diagroomApp/diagroomApp", "packageA/pages/medicalrecord/medicalrecord", "packageA/pages/morereport/morereport", "packageA/pages/feedback/feedback", "packageA/pages/historySuggestion/historySuggestion", "packageA/pages/onlineemrdetail/onlineemrdetail", "packageA/pages/bdMemberManage/bdMemberManage", "packageA/pages/bloodRecord/bloodRecord", "packageA/pages/bloodSugar/bloodSugar", "packageA/pages/bloodRecordDetail/bloodRecordDetail", "packageA/pages/bloodSugarResult/bloodSugarResult", "packageA/pages/bloodPressureRecord/bloodPressureRecord", "packageA/pages/bloodPressureResult/bloodPressureResult", "packageA/pages/bloodPressureRecordDetail/bloodPressureRecordDetail", "packageA/pages/location/location", "packageA/pages/notice/notice", "packageA/pages/mycasehistory/mycasehistory", "packageA/pages/casedetail/casedetail", "packageA/pages/onlineemrdetailhp/onlineemrdetailhp", "packageA/pages/reserve/reservedepartments/reservedepartments", "packageA/pages/reserve/reservedoctorlist/reservedoctorlist", "packageA/pages/reserve/reserveconfirm/reserveconfirm", "packageA/pages/reserve/reservesuccess/reservesuccess", "packageA/pages/reserve/appointmentrecord/appointmentrecord", "packageA/pages/reserve/registrationmask/registrationmask", "packageA/pages/reserve/cancelreason/cancelreason", "packageA/pages/reserve/appointmentRegisterDetail/appointmentRegisterDetail", "packageA/pages/reserve/bkjd/bkjd", "packageA/pages/reserve/onlinePutOnRecord/onlinePutOnRecord", "packageA/pages/todayReserve/registerdepartments/registerdepartments", "packageA/pages/todayReserve/registerdoctorlist/registerdoctorlist", "packageA/pages/todayReserve/registerconfirm/registerconfirm", "packageA/pages/todayReserve/registerpay/registerpay", "packageA/pages/todayReserve/registerpayresult/registerpayresult", "packageA/pages/report/reportList/reportList", "packageA/pages/report/lisReportDetail/lisReportDetail", "packageA/pages/report/pacsReportDetail/pacsReportDetail", "packageA/pages/reserve/newsDetail/newsDetail", "packageA/pages/waitingInLine/waitingInLine", "packageA/pages/outpatientPayment/outpatientPaymentList/outpatientPaymentList", "packageA/pages/outpatientPayment/outpatientPaymentDetail/outpatientPaymentDetail", "packageA/pages/outpatientPayment/outpatientPayResult/outpatientPayResult", "packageA/pages/helpCenter/helpList/helpList", "packageA/pages/helpCenter/helpCenterDetail/helpCenterDetail", "packageA/pages/outpatientPayment/outpatientFeesPendingList/outpatientFeesPendingList", "packageA/pages/outpatientPayment/outpatientFeesPendingDetail/outpatientFeesPendingDetail", "packageA/pages/outpatientPayment/createmzjforder/createmzjforder", "packageA/pages/satisfaction/satisfiedList/satisfiedList", "packageA/pages/satisfaction/satisfied/satisfied", "packageA/pages/userAgreement/userAgreement", "packageA/pages/privacyPolicy/privacyPolicy", "packageA/pages/myDoctorIndex/myDoctorIndex", "packageA/pages/zqtys/zqtys", "packageA/pages/yygh/preconsultationquestionnaire/preconsultationquestionnaire", "packageA/pages/yygh/preconsultationquestionnaireshow/preconsultationquestionnaireshow", "packageA/pages/chooseCampus/chooseCampus", "packageA/pages/jzkRechargeOnline/jzkRechargeOnline", "packageA/pages/hospitalization/hospRecordIndex/hospRecordIndex", "packageA/pages/hospitalization/hospitalizationDetail/hospitalizationDetail", "packageA/pages/hospitalization/hospitalDetailFeeByEveryDay/hospitalDetailFeeByEveryDay", "packageA/pages/hospitalization/hospitalDepositDetail/hospitalDepositDetail", "packageA/pages/hospitalization/hospitalDepositRechargeResult/hospitalDepositRechargeResult", "packageA/pages/outpatientPayment/outpatientPayment/paymentMessage", "packageA/pages/jzkRechargeOnline/jzkRechargeOnlineMessage/jzkRechargeOnlineMessage", "packageA/pages/reserve/consultationRecord/consultationRecord", "packageA/pages/healthCardInfo/healthCardInfo", "packageA/pages/healthCardFaceVertify/healthCardFaceVertify", "packageA/pages/webView/webView", "packageA/pages/bannerArticle/bannerArticleList/bannerArticleList", "packageA/pages/bannerArticle/bannerArticleDetail/bannerArticleDetail", "packageB/pages/patientadd/patientadd", "packageB/pages/patientdetail/patientdetail", "packageB/pages/patientquery/patientquery", "packageB/pages/hospInfo/hospInfo", "packageB/pages/editHomeBtn/editHomeBtn", "packageB/pages/seldepartment/seldepartment", "packageB/pages/selectDepts/selectDepts", "packageB/pages/createorder/createorder", "packageB/pages/createzfcforder/createzfcforder", "packageB/pages/followDoctors/followDoctors", "packageB/pages/jfjlinfowddd/jfjlinfowddd", "packageB/pages/docdetail/docdetail", "packageB/pages/jfjlinfo/jfjlinfo", "packageB/pages/wzdoctors/wzdoctors", "packageB/pages/xzprescriptionywlz/xzprescriptionywlz", "packageB/pages/updateuserphone/updateuserphone", "packageB/pages/userinfo/userinfo", "packageB/pages/settlepara/settlepara", "packageB/pages/popwindow/popwindow", "packageB/pages/jcjyreservedetail/jcjyreservedetail", "packageB/pages/logistics/logistics", "packageB/pages/deptInfo/deptInfo", "packageB/pages/patientedit/patientedit", "packageB/pages/selhospital/selhospital", "packageB/pages/physicalRemind/physicalRemind", "packageB/pages/physicalRemindadd/physicalRemindadd", "packageB/pages/doctorRate/doctorRate", "packageB/pages/diagnosisInfowj/diagnosisInfowj", "packageB/pages/medicineRemind/medicineRemind", "packageB/pages/menstrualManage/menstrualManage", "packageB/pages/search/search", "packageB/pages/searchByType/searchByType", "packageB/pages/subscribeinfo/subscribeinfo", "packageB/pages/lookForExpert/lookForExpert", "packageB/pages/subscriberesult/subscriberesult", "packageB/pages/emrPrintApplyList/emrPrintApplyList", "packageB/pages/emrPrintApply/emrPrintApply", "packageB/pages/emrPrintApplyDetail/emrPrintApplyDetail", "packageB/pages/emrPrintApplyUpdate/emrPrintApplyUpdate", "packageB/pages/sqlNoPersonInfo/sqlNoPersonInfo", "packageB/pages/healthRecord/healthRecord", "packageB/pages/recordWeight/recordWeight", "packageB/pages/recordPressure/recordPressure", "packageB/pages/recordHeartRate/recordHeartRate", "packageB/pages/recordBloodSugar/recordBloodSugar", "packageB/pages/recordWeightResult/recordWeightResult", "packageB/pages/recordBloodPressureResult/recordBloodPressureResult", "packageB/pages/recordBloodSugarResult/recordBloodSugarResult", "packageB/pages/healthRecResult/healthRecResult"], "page": {"pages/customindex/customindex.html": {"window": {"navigationStyle": "custom", "enablePullDownRefresh": false, "navigationBarTitleText": "", "backgroundColor": "#ddd"}}, "pages/index/index.html": {"window": {}}, "pages/mine/mine.html": {"window": {"navigationBarTitleText": "个人中心"}}, "pages/registrationNotice1/registrationNotice1.html": {"window": {"navigationBarTitleText": "挂号须知"}}, "pages/register/register.html": {"window": {"navigationBarTitleText": "选择医生"}}, "pages/login/login.html": {"window": {}}, "pages/doctorInfo/doctorInfo.html": {"window": {"navigationBarTitleText": "医生主页"}}, "pages/diagnosisroom/diagnosisroom.html": {"window": {"navigationStyle": "default", "navigationBarTitleText": "问诊室"}}, "pages/diagnosisInfo/diagnosisInfo.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "诊断详情", "navigationBarBackgroundColor": "#6281FA", "navigationBarTextStyle": "white"}}, "pages/wzrecords/wzrecords.html": {"window": {"navigationBarTitleText": "问诊室"}}, "pages/drugsDetail/drugsDetail.html": {"window": {"navigationBarTitleText": "药品明细"}}, "pages/perinfojfjl/perinfojfjl.html": {"window": {"navigationBarTitleText": "交费记录"}}, "pages/prescription/prescription.html": {"window": {"onReachBottomDistance": 50, "navigationBarTitleText": "我的处方", "enablePullDownRefresh": true}}, "pages/prescriptioninfo/prescriptioninfo.html": {"window": {"pageOrientation": "auto", "navigationBarTitleText": "诊断详情", "navigationBarBackgroundColor": "#6281FA", "navigationBarTextStyle": "white", "navigationStyle": "custom"}}, "pages/room/room.html": {"window": {"navigationBarTitleText": "视频通话", "disableScroll": true, "navigationStyle": "default"}}, "pages/myPrescription/myPrescription.html": {"window": {"navigationBarTitleText": "我的订单"}}, "pages/registration/registration.html": {"window": {"navigationBarTitleText": "预约挂号"}}, "pages/registrationDoc/registrationDoc.html": {"window": {"navigationBarTitleText": "预约挂号"}}, "pages/registeredinfo/registeredinfo.html": {"window": {"navigationBarTitleText": "确认挂号信息"}}, "pages/reservationrecord/reservationrecord.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "预约记录", "enablePullDownRefresh": true}}, "pages/reservationcancel/reservationcancel.html": {"window": {}}, "pages/reservationinfo/reservationinfo.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "预约信息", "enablePullDownRefresh": true}}, "pages/reservationpay/reservationpay.html": {"window": {"navigationBarTitleText": "挂号费用支付"}}, "pages/onlinMedicine/onlinMedicine.html": {"window": {"navigationBarTitleText": "线上拿药"}}, "pages/custommine/custommine.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "个人中心"}}, "pages/notice/notice.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "消息", "enablePullDownRefresh": true}}, "pages/messageDetail/messageDetail.html": {"window": {"navigationBarTitleText": "消息详情"}}, "packageA/pages/address/address.html": {"window": {"navigationBarTitleText": "地址管理"}}, "packageA/pages/addressmanage/addressmanage.html": {"window": {"navigationBarTitleText": "地址管理"}}, "packageA/pages/articlelist/articlelist.html": {"window": {"navigationBarTitleText": "医护好文"}}, "packageA/pages/article/article.html": {"window": {"navigationBarTitleText": "医护好文"}}, "packageA/pages/articalinfo/articalinfo.html": {"window": {}}, "packageA/pages/addMedicineRemind/addMedicineRemind.html": {"window": {"navigationBarTitleText": "新增用药提醒"}}, "packageA/pages/allarticle/allarticle.html": {"window": {"navigationBarTitleText": "文章列表"}}, "packageA/pages/diagroomApp/diagroomApp.html": {"window": {"navigationBarTitleText": "问诊室"}}, "packageA/pages/medicalrecord/medicalrecord.html": {"window": {"navigationBarTitleText": "历史病历"}}, "packageA/pages/morereport/morereport.html": {"window": {"navigationBarTitleText": "报告列表"}}, "packageA/pages/feedback/feedback.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "意见反馈"}}, "packageA/pages/historySuggestion/historySuggestion.html": {"window": {"component": true, "navigationBarTitleText": "历史反馈"}}, "packageA/pages/onlineemrdetail/onlineemrdetail.html": {"window": {"navigationBarTitleText": "病历详情"}}, "packageA/pages/bdMemberManage/bdMemberManage.html": {"window": {"navigationBarTitleText": "血糖管理成员"}}, "packageA/pages/bloodRecord/bloodRecord.html": {"window": {"navigationBarTitleText": "血糖记录"}}, "packageA/pages/bloodSugar/bloodSugar.html": {"window": {"navigationBarTitleText": "血糖管理"}}, "packageA/pages/bloodRecordDetail/bloodRecordDetail.html": {"window": {"navigationBarTitleText": "血糖记录"}}, "packageA/pages/bloodSugarResult/bloodSugarResult.html": {"window": {"navigationBarTitleText": "血糖"}}, "packageA/pages/bloodPressureRecord/bloodPressureRecord.html": {"window": {"navigationBarTitleText": "血压记录"}}, "packageA/pages/bloodPressureResult/bloodPressureResult.html": {"window": {"navigationBarTitleText": "血压"}}, "packageA/pages/bloodPressureRecordDetail/bloodPressureRecordDetail.html": {"window": {"navigationBarTitleText": "血压记录"}}, "packageA/pages/location/location.html": {"window": {}}, "packageA/pages/notice/notice.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "消息", "enablePullDownRefresh": true}}, "packageA/pages/mycasehistory/mycasehistory.html": {"window": {"navigationBarTitleText": "我的病历"}}, "packageA/pages/casedetail/casedetail.html": {"window": {"navigationBarTitleText": "病历详情"}}, "packageA/pages/onlineemrdetailhp/onlineemrdetailhp.html": {"window": {"navigationBarTitleText": "病历详情", "pageOrientation": "landscape"}}, "packageA/pages/reserve/reservedepartments/reservedepartments.html": {"window": {"backgroundColor": "#ddd", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "navigationBarTitleText": "科室列表"}}, "packageA/pages/reserve/reservedoctorlist/reservedoctorlist.html": {"window": {"backgroundColor": "#ddd", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "navigationBarTitleText": "选择医生"}}, "packageA/pages/reserve/reserveconfirm/reserveconfirm.html": {"window": {"backgroundColor": "#ddd", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "navigationBarTitleText": "确认预约信息"}}, "packageA/pages/reserve/reservesuccess/reservesuccess.html": {"window": {"backgroundColor": "#ddd", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "navigationBarTitleText": "预约成功"}}, "packageA/pages/reserve/appointmentrecord/appointmentrecord.html": {"window": {"navigationBarTitleText": "预约记录", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black"}}, "packageA/pages/reserve/registrationmask/registrationmask.html": {"window": {"backgroundColor": "#ddd", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "navigationBarTitleText": "医生详情"}}, "packageA/pages/reserve/cancelreason/cancelreason.html": {"window": {"component": true, "backgroundColor": "#ddd", "navigationBarTitleText": "选择取消原因"}}, "packageA/pages/reserve/appointmentRegisterDetail/appointmentRegisterDetail.html": {"window": {"component": true, "backgroundColor": "#ddd", "navigationBarTitleText": "预约挂号详情信息"}}, "packageA/pages/reserve/bkjd/bkjd.html": {"window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "navigationBarTitleText": "绑卡/建档"}}, "packageA/pages/reserve/onlinePutOnRecord/onlinePutOnRecord.html": {"window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "navigationBarTitleText": "在线建档"}}, "packageA/pages/todayReserve/registerdepartments/registerdepartments.html": {"window": {"backgroundColor": "#ddd", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "navigationBarTitleText": "当日挂号科室列表"}}, "packageA/pages/todayReserve/registerdoctorlist/registerdoctorlist.html": {"window": {"backgroundColor": "#ddd", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "navigationBarTitleText": "当日挂号医生列表"}}, "packageA/pages/todayReserve/registerconfirm/registerconfirm.html": {"window": {"backgroundColor": "#ddd", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "navigationBarTitleText": "确认挂号"}}, "packageA/pages/todayReserve/registerpay/registerpay.html": {"window": {"backgroundColor": "#ddd", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "navigationBarTitleText": "挂号交费"}}, "packageA/pages/todayReserve/registerpayresult/registerpayresult.html": {"window": {"backgroundColor": "#ddd", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black", "navigationBarTitleText": "挂号成功"}}, "packageA/pages/report/reportList/reportList.html": {"window": {"enablePullDownRefresh": true, "navigationBarTitleText": "报告查询"}}, "packageA/pages/report/lisReportDetail/lisReportDetail.html": {"window": {"navigationBarTitleText": "报告详情"}}, "packageA/pages/report/pacsReportDetail/pacsReportDetail.html": {"window": {"navigationBarTitleText": "报告详情"}}, "packageA/pages/reserve/newsDetail/newsDetail.html": {"window": {"navigationBarTitleText": "消息详情"}}, "packageA/pages/waitingInLine/waitingInLine.html": {"window": {"navigationBarTitleText": "排队查询"}}, "packageA/pages/outpatientPayment/outpatientPaymentList/outpatientPaymentList.html": {"window": {"enablePullDownRefresh": true, "navigationBarTitleText": "门诊费用查询"}}, "packageA/pages/outpatientPayment/outpatientPaymentDetail/outpatientPaymentDetail.html": {"window": {"navigationBarTitleText": "费用详情"}}, "packageA/pages/outpatientPayment/outpatientPayResult/outpatientPayResult.html": {"window": {"navigationBarTitleText": "交费结果"}}, "packageA/pages/helpCenter/helpList/helpList.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "帮助中心"}}, "packageA/pages/helpCenter/helpCenterDetail/helpCenterDetail.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "帮助中心"}}, "packageA/pages/outpatientPayment/outpatientFeesPendingList/outpatientFeesPendingList.html": {"window": {"navigationBarTitleText": "门诊待交费列表", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black"}}, "packageA/pages/outpatientPayment/outpatientFeesPendingDetail/outpatientFeesPendingDetail.html": {"window": {"navigationBarTitleText": "处方详情"}}, "packageA/pages/outpatientPayment/createmzjforder/createmzjforder.html": {"window": {"navigationBarBackgroundColor": "#3B71E8", "navigationBarTitleText": "确认订单"}}, "packageA/pages/satisfaction/satisfiedList/satisfiedList.html": {"window": {"navigationBarTitleText": "问卷调查列表"}}, "packageA/pages/satisfaction/satisfied/satisfied.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "满意度调查 "}}, "packageA/pages/userAgreement/userAgreement.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "用户协议"}}, "packageA/pages/privacyPolicy/privacyPolicy.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "隐私政策"}}, "packageA/pages/myDoctorIndex/myDoctorIndex.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "我的医生"}}, "packageA/pages/zqtys/zqtys.html": {"window": {}}, "packageA/pages/yygh/preconsultationquestionnaire/preconsultationquestionnaire.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "预问诊", "enablePullDownRefresh": false}}, "packageA/pages/yygh/preconsultationquestionnaireshow/preconsultationquestionnaireshow.html": {"window": {"enablePullDownRefresh": false, "navigationBarTitleText": "预问诊"}}, "packageA/pages/chooseCampus/chooseCampus.html": {"window": {"navigationBarTitleText": "选择院区"}}, "packageA/pages/jzkRechargeOnline/jzkRechargeOnline.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "就诊卡充值"}}, "packageA/pages/hospitalization/hospRecordIndex/hospRecordIndex.html": {"window": {"navigationBarTitleText": "住院查询"}}, "packageA/pages/hospitalization/hospitalizationDetail/hospitalizationDetail.html": {"window": {"navigationBarTitleText": "住院详情"}}, "packageA/pages/hospitalization/hospitalDetailFeeByEveryDay/hospitalDetailFeeByEveryDay.html": {"window": {"navigationBarTitleText": "费用明细"}}, "packageA/pages/hospitalization/hospitalDepositDetail/hospitalDepositDetail.html": {"window": {"navigationBarTitleText": "押金记录"}}, "packageA/pages/hospitalization/hospitalDepositRechargeResult/hospitalDepositRechargeResult.html": {"window": {"navigationBarTitleText": "押金交纳结果"}}, "packageA/pages/outpatientPayment/outpatientPayment/paymentMessage.html": {"window": {"navigationBarTitleText": "消息详情"}}, "packageA/pages/jzkRechargeOnline/jzkRechargeOnlineMessage/jzkRechargeOnlineMessage.html": {"window": {}}, "packageA/pages/reserve/consultationRecord/consultationRecord.html": {"window": {"navigationBarTitleText": "就诊记录", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black"}}, "packageA/pages/healthCardInfo/healthCardInfo.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "电子健康卡信息"}}, "packageA/pages/healthCardFaceVertify/healthCardFaceVertify.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "人脸核身"}}, "packageA/pages/webView/webView.html": {"window": {}}, "packageA/pages/bannerArticle/bannerArticleList/bannerArticleList.html": {"window": {"navigationBarTitleText": "文章列表"}}, "packageA/pages/bannerArticle/bannerArticleDetail/bannerArticleDetail.html": {"window": {"navigationBarTitleText": "文章详情"}}, "packageB/pages/patientadd/patientadd.html": {"window": {"navigationBarTitleText": "添加就诊人"}}, "packageB/pages/patientdetail/patientdetail.html": {"window": {"navigationBarTitleText": "编辑就诊人"}}, "packageB/pages/patientquery/patientquery.html": {"window": {"navigationBarTitleText": "就诊人管理"}}, "packageB/pages/hospInfo/hospInfo.html": {"window": {"navigationBarTitleText": "医院信息", "backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black"}}, "packageB/pages/editHomeBtn/editHomeBtn.html": {"window": {}}, "packageB/pages/seldepartment/seldepartment.html": {"window": {"backgroundColor": "#F7F7F7", "navigationBarTitleText": "选择科室", "enablePullDownRefresh": false}}, "packageB/pages/selectDepts/selectDepts.html": {"window": {"navigationBarTitleText": "选择科室"}}, "packageB/pages/createorder/createorder.html": {"window": {"navigationBarTitleText": "挂号交费"}}, "packageB/pages/createzfcforder/createzfcforder.html": {"window": {"navigationBarTitleText": "处方交费"}}, "packageB/pages/followDoctors/followDoctors.html": {"window": {"navigationBarTitleText": "关注医生"}}, "packageB/pages/jfjlinfowddd/jfjlinfowddd.html": {"window": {"pageOrientation": "auto", "navigationBarTitleText": "订单详情", "navigationBarBackgroundColor": "#6281FA", "navigationBarTextStyle": "white", "navigationStyle": "custom"}}, "packageB/pages/docdetail/docdetail.html": {"window": {"navigationBarTitleText": "医生详情"}}, "packageB/pages/jfjlinfo/jfjlinfo.html": {"window": {"navigationBarTitleText": "订单详情"}}, "packageB/pages/wzdoctors/wzdoctors.html": {"window": {"navigationBarTitleText": "选择医生"}}, "packageB/pages/xzprescriptionywlz/xzprescriptionywlz.html": {"window": {"navigationBarTitleText": "处方院外流转", "component": true}}, "packageB/pages/updateuserphone/updateuserphone.html": {"window": {"navigationBarTitleText": "修改手机号"}}, "packageB/pages/userinfo/userinfo.html": {"window": {"navigationBarTitleText": "修改用户基础信息"}}, "packageB/pages/settlepara/settlepara.html": {"window": {"navigationBarTitleText": "处方预结算"}}, "packageB/pages/popwindow/popwindow.html": {"window": {"navigationBarTitleText": "结算查询中..."}}, "packageB/pages/jcjyreservedetail/jcjyreservedetail.html": {"window": {"navigationBarTitleText": "检查检验详情"}}, "packageB/pages/logistics/logistics.html": {"window": {"navigationBarTitleText": "配送信息"}}, "packageB/pages/deptInfo/deptInfo.html": {"window": {"navigationBarTitleText": "科室介绍"}}, "packageB/pages/patientedit/patientedit.html": {"window": {"navigationBarTitleText": "编辑就诊人"}}, "packageB/pages/selhospital/selhospital.html": {"window": {"navigationBarTitleText": "选择医院"}}, "packageB/pages/physicalRemind/physicalRemind.html": {"window": {"navigationBarTitleText": "体检提醒"}}, "packageB/pages/physicalRemindadd/physicalRemindadd.html": {"window": {"navigationBarTitleText": "新增管理成员"}}, "packageB/pages/doctorRate/doctorRate.html": {"window": {"navigationBarTitleText": "评分"}}, "packageB/pages/diagnosisInfowj/diagnosisInfowj.html": {"window": {"navigationStyle": "custom", "navigationBarTitleText": "诊断详情", "navigationBarBackgroundColor": "#6281FA", "navigationBarTextStyle": "white"}}, "packageB/pages/medicineRemind/medicineRemind.html": {"window": {"navigationBarTitleText": "用药提醒"}}, "packageB/pages/menstrualManage/menstrualManage.html": {"window": {"navigationBarTitleText": "经期管理"}}, "packageB/pages/search/search.html": {"window": {"navigationBarTitleText": "搜索"}}, "packageB/pages/searchByType/searchByType.html": {"window": {"navigationBarTitleText": "搜索"}}, "packageB/pages/subscribeinfo/subscribeinfo.html": {"window": {"navigationBarTitleText": "确认预约信息"}}, "packageB/pages/lookForExpert/lookForExpert.html": {"window": {"navigationBarTitleText": "查找专家"}}, "packageB/pages/subscriberesult/subscriberesult.html": {"window": {"navigationBarTitleText": "预约结果"}}, "packageB/pages/emrPrintApplyList/emrPrintApplyList.html": {"window": {"navigationBarTitleText": "病案复印申请记录"}}, "packageB/pages/emrPrintApply/emrPrintApply.html": {"window": {"navigationBarTitleText": "病案复印申请"}}, "packageB/pages/emrPrintApplyDetail/emrPrintApplyDetail.html": {"window": {"navigationBarTitleText": "申请详情"}}, "packageB/pages/emrPrintApplyUpdate/emrPrintApplyUpdate.html": {"window": {"navigationBarTitleText": "病案复印申请修改"}}, "packageB/pages/sqlNoPersonInfo/sqlNoPersonInfo.html": {"window": {"navigationBarTitleText": "处方流转"}}, "packageB/pages/healthRecord/healthRecord.html": {"window": {"backgroundColor": "#ddd", "navigationBarTitleText": "健康记录"}}, "packageB/pages/recordWeight/recordWeight.html": {"window": {"navigationBarTitleText": "体重记录"}}, "packageB/pages/recordPressure/recordPressure.html": {"window": {"navigationBarTitleText": "血压记录"}}, "packageB/pages/recordHeartRate/recordHeartRate.html": {"window": {"navigationBarTitleText": "心率记录"}}, "packageB/pages/recordBloodSugar/recordBloodSugar.html": {"window": {"navigationBarTitleText": "血糖记录"}}, "packageB/pages/recordWeightResult/recordWeightResult.html": {"window": {"navigationBarTitleText": "体重记录结果"}}, "packageB/pages/recordBloodPressureResult/recordBloodPressureResult.html": {"window": {"navigationBarTitleText": "血压记录结果"}}, "packageB/pages/recordBloodSugarResult/recordBloodSugarResult.html": {"window": {"navigationBarTitleText": "血糖记录结果"}}, "packageB/pages/healthRecResult/healthRecResult.html": {"window": {"navigationBarTitleText": "健康数据"}}}, "global": {"window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTextStyle": "black"}}, "tabBar": {"color": "#B6B8BC", "selectedColor": "#333333", "backgroundColor": "#ffffff", "borderStyle": "white", "list": [{"text": "首页", "pagePath": "pages/customindex/customindex.html", "iconData": "iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAKKADAAQAAAABAAAAKAAAAAB65masAAAFJ0lEQVRYCc1YXWgcVRS+Z/bHTX9NazBJ3d3mZ5uNUFExaG0RQUR9kVQEsRZF8lyJoBQpilqwVWx9EB/6IPgDIVawNaIvEjAIFsyjmt3N/rE7SRrSYNWkZpPM3Ot3N97ZSZxNdjZrs/Ny7pxz7jnfnHPuPfcOsRo9Y8lcPwn2EiM2rWme17s67hiuhWnarJFsVgQKhv6xEOKYskVEJoCe7O4MnVO8aummAMbz+VZRYJcFEz2OAIi+2LPT39fc3HzDUV4Bs2qAiXS+h5tFcK3KD4xlEbm9QrBdFo/oFy9jRyORUFrx3FDNjbLSjaXzz3LORhA5CxxkwzsatPuY5pHRjCldpP7gMhOj8ZT+uOK5oa4iCGcUS+qnGROn7E5Qcx9FO4P9oIbkx69d2ymuL3yCD3hK6UHGidgbXR3BdzAWir8RrRjgzMzMjtk/C58zIXptRpdJYye6O8MXbLziUH4MovYaXk5jbGWKGF2ixoYXok1Nc2vnOL1XBDCTmQovGsYQInKXMgJHs4zo6e5IcETxnKhMLRd8gAnWaJPHNK/3aLR9X8LGcxxuCDCRzh3hnL5CFJqUBaToV/L5n4zub84q3np0PJdrN5foMmwcVHpI91/4yOejkdDXiudErdA7CeNJvc802fAacEN7dwcOVQpO2j0QDmd2b/c+AECDyo9c6SjES/FU/m17CSi5oo4RxARPIqW/z4XoV4qSInJnsBhOuSly+3w5jqXyryDdZ6UPJQOIbwO+Xcfb2hr/UDxF/wMwm71+a8GYG4SBxywlYgUmtL7uA8EBxdsMTaQnHuEmH0RN32bZIUr5mOiNRMK/WTwMVgFMp/XIksm/Qei7lBLSMqV5WG9XR2hU8WpBiwvPXEZts3tL9uiGRuLFaCT8peJZNTg2nnt0kfOf14AbpQDrqTU46by9vTUX8IYOo1w+U2Cwv27ngl0cS+XeVSVQjGAspZ/A/vaBYsoJmDgQ8Ab72tqoUDLw/4ykf8HFeQBEV1x54P/7bX7xDCFy74H1qk3AZafojoTPKt7NoOOZ/EOGKS5iAd2u/KG8PpUAl8DwSSbCOQfkx7E3DSmlm0kTur6PL/BRlFlLEQ/RiKzBIrgVIHRhq8BJ/13B4CSiNLWCBXkUzGMtEskUmkB6t/ZBCzcVApwptFUAlWArKVqgLUhUfwCRRyuCWDCrU7yVkVO+keJSBOs0xVYE5SKxNkb1BW4oNnZfJnO1BXWzqmUqGz6fORcMBn9X75XR0kKFWa0qgOPZibtNwzwXS+aPwKm/nOMCLgCx8fwUI/FhFFdQ7LHL5XRLfLmKsRPiEVRFBJPJyXsMw7iC8N9SMlp+VLxYCXYGJ2t5WH2uvKYlKdUgrgqutxmDmecrBWe5xADlcCyemjxs5zmNoWfVIOrGHUBMlh90v5Phynj8wY30UAYWQEHkepvxAmTDRk7KyTkX1oW+nA6qz0oxVZPicoZrxceN2RbBOtyoseKtCOJ85a4GaxWl9ewIVqpBRNN1Da5nuyYygLIiKOqx1QGUVYPoJPUYwVKK0VDqrwbR1Vel2Ivr29/Y5rfJAsIPspCu63s4Niyngpqenq+qdytbOFR45I8B9b6Wer3z2nxBFO8jUkaCFmgsmf8OAJ9Yq+z0jtazBORlDwdOc+w8dIkfsNE/bOetN0YNDuKHvOdl+adpPcWtkAHcrN/jPVk8x/37/+9NtPRDqIEWFKfj+Y5IWxSCV3SKcfwoop+QrfL9mCBlbAKp/VH4G966c3/T1X8A8Zf0aZN7UDYAAAAASUVORK5CYII=", "selectedIconData": "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"}, {"text": "消息", "pagePath": "pages/notice/notice.html", "iconData": "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", "selectedIconData": "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"}, {"text": "我的", "pagePath": "pages/custommine/custommine.html", "iconData": "iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAKKADAAQAAAABAAAAKAAAAAB65masAAAENElEQVRYCe2YS2hUZxTHv3NnxoyhgRZxYiQzOm3GDBZrwYWirgqGqguRIriRbiwiIpTixo1m142IoCELu3NTcSEt+MCCID7AheA7oxObdiYdY0QQU9I85s7xfyZzJjfNnUy+a/NYdCCc757vnPP/zfe8EzIf+Mlm//zcNbS/ZLjDMCXK5YhzjqFrIcM/pVKrnnyIBAVNZuYlmd78SeQfRNvxq0NEJfi7023xH9Ae84up5wsEOAGXu8JsvqonIP1E5nq6LbE9CKTvN68nKiM3WzipJbGV0a5Xelq/9QjKmisaelhrWqcpVBwy3WHDX9iuSesRlA1hCyeMkiO5tb5ALb81YHm31qpWxx8k1xqwepTUgfHt1mPIt9PfaQ/oX2fOvPaAOIQD0wTItQaUGyIoYJBca0C5vio3hBWn5EiuVRKCrQEr51i3rRDiu23PQNGwPqgladFfdXKnVu7WrpmmW/rw1xX0Hg48gpKon7l+3VKd/23QEQi6SejZ7/kNXKId2DHrDXEzG1ph2Kwog5AZIMMDuBZf4WXwATl8uf3T+D2sR7YFtQJ83tf/pTte+h5A2/GOF7MRw0vrIICvhCLOqTXJ1vuzzZ0VYM+L/DrjcicOpd04YmaVUwugPIpsLuJHwvG1bYnHteLUP6MYYCKZbO4EpunwNDCiYRT5DYKXQg4/CzG9bGpqeCmFh4ZGW1ziFrdE7cjbCdc2LIVG6dPPBCifTqcSR9AeV/+/bU3ATC63kkf5AqZyszcJCRnHoc4lofgvySSNePtqtfv6ODrm5neVStyJRZj2xmHq71AD7UknEgWvX9u+gLLWikX3KhZ9swZiegfwTY+mP4ufg3WrfosGRjOUeZHfB/tjdUNJPplX4XDoa7+1OQ0wm+1vLXLpLhteqdr4lrc50rhn7erl5SlUf1D79I/XLTQ+LLOzRWuQoUKYnI2pVGu/+sROARwcHPzozduRW4Bbr0GAO4ur6tBM60RjbSxGMZLpzXUB8jvNA+SDZR9Ht8Zisb/VN+VtBnBnvXDYHOcBd+C/hhNxqSm1RUNhRFsY9FlsFTDT+9cWBOzVThS4ETHxb2GtD1etUc9K7YrGDY0VBmHR5yogs3tCnZjWd9FwdG8qRaPqmysrGqIlmqrhZSkD9jzPf4M1sUkDMGbHksnYgD7PtRUt0VQdYREmeS4DMvE+7cSaeNKeSpypPs9To6wJbZVTJqdQKDRiK3doh8PmDNZGoHNOawSxoinamitMwuYMDRc7MKRLpQPrYKQh0vSzBs23FW1hEF1hEjbH5cnRwyvTr8nkJ2/nG0z1RFsY9FnYHGKe+K8ovA7Rbe1cKOtlEDb8ljYtkzD0aLK9UC0PA9iwLk31zuWoebhQWKrrZRA26snmhnWTaNBisdjZ/8g5eHOxAPlw3HQiFNoPUvmH0JhPwEK5xoRJ2N4DwtiwKWHrAz0AAAAASUVORK5CYII=", "selectedIconData": "iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAKKADAAQAAAABAAAAKAAAAAB65masAAAEQ0lEQVRYCe2YT2wUVRzHf783M90VWoxBA6RlL9qklaQHDoabaIyJHLzRkxewJVGjBxPhADGjgQMcPGBATZWTp8LBECIJMVRvpAcORGxN1cSCEURCAlvo7s57P3+/ad5kt93Z3ffIVA/O5c289/v9vp/3e/9mBuExr8mPaYeGZAKIXkXAioQjoEVAvBRA+OXUh3j9cSTQ1zmepr7FOf0JAr3FQKpdHAY2BPhZZTR4Px7HejubbnVegCncfHKRs/ZyN4G0HfFyZSR8zQeybc+7iUrmeoaTYNyR1Kdb4DbtzhmUOWcouZY3rG000ioZboXhmOucdM6gLAhXOCEUn3Qx5fUgp94ZUFZrTqzu1R6+zoB2K+lOs9bCx9cZcK1ssTXOgDyXFn2RfHydAeWE8AX08XUGlONLtgxXSPERX1c/Z0DZx+T4chUSH9c9UDScAcVJzlYersty39MlR534eFxegHKmytnK/TvVabhX2tQp33NY+uN81K1OQtGvW6v1/n92zYDXEBMRvnkCtgW1ZJjIbCUMNiKYfgPYLwAKqEqgqkh6CVHd0qVw4auD8CciUqGAb5+grbWa3kWGhllog6PYQ1S4UCoFV04fxFu9+vaUwYljtAWS+m4DaqTXwJ3sFJj5cEPfzOcf4F+d7KStI+DeaQoG5jS/XtELqwPx/pQQ4q88tAukor/LCqrPPAcPxO7OLzCwbKAfTeNpHuphJHqWj55wdQyWn30wGlw6O456bdtKTS7g/uM0AMuNcQ4y1OzMYncZ+PvtL/bNxy9h0tyWdx/PUHjjhzpnH3dzpza32tFNKEfTZw5h2rnWtpwMpnNtOXmDCDZaB57eSyoKvxtswLU4dj+LJU4ck/ojgjHTSF4hbIqNsFQqh1+3m5trMvjuSdr06F4yyUOSrkgJzKvvBpWCs3m9FBuXS0YHa3ov7wbbrR9PmeoTT4VTn76H922dlC2AK5+Tjf1AuMUa8XF19f5o8G2neWJtXUqZ35vm9B5+R9yZ+SHdroxEZ5o/T1sm7s2f6q8DqgxOKbw+dSS44LN/ZaI5N9JhzuCFyaO6ZAztSM04MSkDwDnrlr0sHIgfVQyqFUNpRfx9SAffFAFnxSW2aIiWrRMGYbHPGaCGIPta43GvlzcH53gx9LRKbTCfUjRESzStfzNLCrgvrj3Py3/QGvBWMnP6Haza56JL0RJNqyMswiTPKaBSwZht5KzfGYJo1j6vVymaom31LJM68AVFstPbBv5PNeu7z9kYPqVoirb1FSZhU3AXsmOIN2NdGYx+tEbrXYq2MIhuejQym9INnWWPP7x+jvfh8nqDWb1Umxnss7Ap3qqftBUB4qK9/7fKFgZm43dLM2BhDOqurz/WtqiyhYHZGBAzQKXLt4sS7jVuCwOz4cRHjcPt39V6DVmcHe+BifL5oVMcUmtkYVNBKTrPf+p/4yZO5H/mMsIkbP8AGjiq8Tp0rsgAAAAASUVORK5CYII="}]}, "renderer": {"allUsed": ["webview"], "default": "webview"}, "requiredPrivateInfos": ["getLocation"], "__usePrivacyCheck__": false, "componentFramework": {"allUsed": ["exparser"], "default": "exparser"}}