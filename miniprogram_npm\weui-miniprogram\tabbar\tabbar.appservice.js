$gwx_XC_19=function(_,_v,_n,_p,_s,_wp,_wl,$gwn,$gwl,$gwh,wh,$gstack,$gwrt,gra,grb,TestTest,wfor,_ca,_da,_r,_rz,_o,_oz,_1,_1z,_2,_2z,_m,_mz,nv_getDate,nv_getRegExp,nv_console,nv_parseInt,nv_parseFloat,nv_isNaN,nv_isFinite,nv_decodeURI,nv_decodeURIComponent,nv_encodeURI,nv_encodeURIComponent,$gdc,nv_JSON,_af,_gv,_ai,_grp,_gd,_gapi,$ixc,_ic,_w,_ev,_tsd){return function(path,global){
if(typeof global==='undefined'){if (typeof __GWX_GLOBAL__==='undefined')global={};else global=__GWX_GLOBAL__;}var __WXML_GLOBAL__={};{
}__WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
var e_={}
if(typeof(global.entrys)==='undefined')global.entrys={};e_=global.entrys;
var d_={}
if(typeof(global.defines)==='undefined')global.defines={};d_=global.defines;
var f_={}
if(typeof(global.modules)==='undefined')global.modules={};f_=global.modules || {};
var p_={}
__WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
__WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
__WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
var z=__WXML_GLOBAL__.ops_set.$gwx_XC_19 || [];
function gz$gwx_XC_19_1(){
if( __WXML_GLOBAL__.ops_cached.$gwx_XC_19_1)return __WXML_GLOBAL__.ops_cached.$gwx_XC_19_1
__WXML_GLOBAL__.ops_cached.$gwx_XC_19_1=[];
(function(z){var a=11;function Z(ops){z.push(ops)}
Z([[7],[3,'list']])
Z([3,'index'])
Z([a,[3,'t'],[[7],[3,'index']],[3,'_tips']])
Z([a,z[2][1],z[2][2],[3,'_title']])
Z([3,'tab'])
Z([[2,'==='],[[7],[3,'index']],[[7],[3,'current']]])
Z([3,'tabChange'])
Z([a,[3,'weui-tabbar__item '],[[2,'?:'],[[2,'==='],[[7],[3,'index']],[[7],[3,'current']]],[1,'weui-bar__item_on'],[1,'']]])
Z(z[2][2])
Z([[2,'||'],[[6],[[7],[3,'item']],[3,'badge']],[[6],[[7],[3,'item']],[3,'dot']]])
Z([[2,'||'],[[6],[[7],[3,'item']],[3,'ariaLabel']],[1,'']])
Z([[6],[[7],[3,'item']],[3,'badge']])
Z([3,'position: absolute;top:-2px;left:calc(100% - 3px)'])
})(__WXML_GLOBAL__.ops_cached.$gwx_XC_19_1);return __WXML_GLOBAL__.ops_cached.$gwx_XC_19_1
}
__WXML_GLOBAL__.ops_set.$gwx_XC_19=z;
__WXML_GLOBAL__.ops_init.$gwx_XC_19=true;
var x=['./miniprogram_npm/weui-miniprogram/tabbar/tabbar.wxml'];d_[x[0]]={}
var m0=function(e,s,r,gg){
var z=gz$gwx_XC_19_1()
var aHE=_v()
_(r,aHE)
var tIE=function(bKE,eJE,oLE,gg){
var oNE=_mz(z,'view',['ariaDescribedby',2,'ariaLabelledby',1,'ariaRole',2,'ariaSelected',3,'bindtap',4,'class',5,'data-index',6],[],bKE,eJE,gg)
var fOE=_v()
_(oNE,fOE)
if(_oz(z,9,bKE,eJE,gg)){fOE.wxVkey=1
var cPE=_mz(z,'mp-badge',['ariaLabel',10,'content',1,'style',2],[],bKE,eJE,gg)
_(fOE,cPE)
}
fOE.wxXCkey=1
fOE.wxXCkey=3
_(oLE,oNE)
return oLE
}
aHE.wxXCkey=4
_2z(z,0,tIE,e,s,gg,aHE,'item','index','index')
return r
}
e_[x[0]]=e_[x[0]]||{f:m0,j:[],i:[],ti:[],ic:[]}
if(path&&e_[path]){
return function(env,dd,global){$gwxc=0;var root={"tag":"wx-page"};root.children=[]
;g="$gwx_XC_19";var main=e_[path].f
if (typeof global==="undefined")global={};global.f=$gdc(f_[path],"",1);
try{
main(env,{},root,global);
_tsd(root)
}catch(err){
console.log(err)
}
;g="";
return root;
}
}
}
}(__g.a,__g.b,__g.c,__g.d,__g.e,__g.f,__g.g,__g.h,__g.i,__g.j,__g.k,__g.l,__g.m,__g.n,__g.o,__g.p,__g.q,__g.r,__g.s,__g.t,__g.u,__g.v,__g.w,__g.x,__g.y,__g.z,__g.A,__g.B,__g.C,__g.D,__g.E,__g.F,__g.G,__g.H,__g.I,__g.J,__g.K,__g.L,__g.M,__g.N,__g.O,__g.P,__g.Q,__g.R,__g.S,__g.T,__g.U,__g.V,__g.W,__g.X,__g.Y,__g.Z,__g.aa);if(__vd_version_info__.delayedGwx||false)$gwx_XC_19();	if (__vd_version_info__.delayedGwx) __wxAppCode__['miniprogram_npm/weui-miniprogram/tabbar/tabbar.wxml'] = [$gwx_XC_19, './miniprogram_npm/weui-miniprogram/tabbar/tabbar.wxml'];else __wxAppCode__['miniprogram_npm/weui-miniprogram/tabbar/tabbar.wxml'] = $gwx_XC_19( './miniprogram_npm/weui-miniprogram/tabbar/tabbar.wxml' );
	;__wxRoute = "miniprogram_npm/weui-miniprogram/tabbar/tabbar";__wxRouteBegin = true;__wxAppCurrentFile__="miniprogram_npm/weui-miniprogram/tabbar/tabbar.js";define("miniprogram_npm/weui-miniprogram/tabbar/tabbar.js",function(require,module,exports,window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,XMLHttpRequest,WebSocket,Reporter,webkit,WeixinJSCore){
"use strict";module.exports=require("../_commons/0.js")([{ids:[20],modules:{10:function(e,t,r){e.exports=r(110)},110:function(e,t){Component({options:{addGlobalClass:!0,writeIdToDOM:!0},properties:{extClass:{type:String,value:""},list:{type:Array,value:[]},current:{type:Number,value:0},reactive:{type:Boolean,value:!0}},methods:{tabChange:function(e){var t=e.currentTarget.dataset.index;t!==this.data.current&&(this.setData({current:t}),this.triggerEvent("change",{index:t,item:this.data.list[t]}))}}})}},entries:[[10,0]]}]);
},{isPage:false,isComponent:true,currentFile:'miniprogram_npm/weui-miniprogram/tabbar/tabbar.js'});require("miniprogram_npm/weui-miniprogram/tabbar/tabbar.js");